/**
 * @file WebGL纹理系统 - 企业级GPU纹理资源管理
 *
 * 该文件实现了完整的WebGL纹理管理系统，是现代3D渲染管线的核心组件。
 * 提供高性能的GPU纹理资源管理、多种纹理格式支持和智能状态优化，
 * 实现了从CPU到GPU的高效纹理数据传输和管理。
 *
 * 🔧 **unpackAlignment 内存对齐机制**：
 * 本文件特别注重 unpackAlignment 的正确实现，这是WebGL纹理性能优化的关键。
 * unpackAlignment 控制像素数据从客户端内存传输到GPU显存时的字节对齐方式，
 * 正确的对齐设置可以显著提升数据传输效率和渲染性能。
 *
 * 🎯 **核心功能模块**：
 * - 🖼️ **多源纹理支持**：图像、视频、画布、类型化数组、压缩纹理
 * - 🔄 **智能状态管理**：GPU状态缓存和冗余调用消除
 * - 📐 **多维纹理类型**：2D、立方体贴图、3D纹理、数组纹理
 * - ⚡ **性能优化**：mipmap生成、各向异性过滤、内存对齐
 * - 🔧 **兼容性处理**：WebGL 1.0/2.0差异处理和降级策略
 * - 💾 **内存管理**：GPU显存优化和资源生命周期管理
 *
 * 🔬 **WebGL纹理技术原理**：
 * - **纹理映射**：将2D图像数据映射到3D几何体表面
 * - **过滤算法**：最近邻、线性、三线性、各向异性过滤
 * - **Mipmap技术**：多级渐远纹理，提供距离相关的细节层次
 * - **压缩格式**：DXT、ETC、ASTC等GPU原生压缩格式
 * - **内存布局**：纹理数据在GPU显存中的组织和访问模式
 *
 * 🚀 **性能优化特性**：
 * - 智能状态缓存减少GPU状态切换开销
 * - 延迟更新机制避免不必要的数据传输
 * - 2的幂纹理优化和非2的幂纹理降级处理
 * - 各向异性过滤的硬件适配和性能平衡
 * - 压缩纹理的带宽优化和质量控制
 *
 * 🎮 **应用场景**：
 * - **游戏渲染**：角色贴图、环境纹理、特效贴图
 * - **建筑可视化**：材质纹理、环境映射、光照贴图
 * - **科学可视化**：数据纹理、体积渲染、热力图
 * - **UI渲染**：图标纹理、字体渲染、界面元素
 * - **视频处理**：实时视频纹理、后处理效果
 * - **VR/AR应用**：全景纹理、立体纹理、环境映射
 *
 * 📊 **技术规范**：
 * - 支持WebGL 1.0/2.0标准纹理格式
 * - 兼容主流压缩纹理扩展
 * - 符合OpenGL ES纹理规范
 * - 支持HDR和LDR纹理格式
 * - 自动硬件能力检测和适配
 *
 * 🛡️ **企业级特性**：
 * - 完整的错误处理和边界检查
 * - 详细的性能监控和调试支持
 * - 内存泄漏防护和资源自动清理
 * - 跨平台兼容性和设备适配
 * - 可扩展的纹理格式和过滤器支持
 *
 * <AUTHOR> Framework Team
 * @version 2.0.0
 * @since 2024
 * @license MIT
 *
 * @requires WebGL1/WebGL2 - WebGL渲染上下文
 *
 * @example
 * // 基础图像纹理
 * const imageTexture = new Texture(gl, {
 *   image: imageElement,
 *   generateMipmaps: true,
 *   minFilter: gl.LINEAR_MIPMAP_LINEAR
 * });
 *
 * @example
 * // 数据纹理（用于计算着色器）
 * const dataTexture = new Texture(gl, {
 *   image: new Float32Array(width * height * 4),
 *   width: 512,
 *   height: 512,
 *   type: gl.FLOAT,
 *   format: gl.RGBA,
 *   generateMipmaps: false
 * });
 *
 * @see {@link https://www.khronos.org/webgl/wiki/Tutorial} WebGL教程
 * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API} WebGL API文档
 */

/**
 * 🚀 **开发路线图和技术债务**
 *
 * 以下是当前版本的已知限制和未来改进计划：
 */

// TODO: 🗑️ 实现纹理删除功能 - 添加dispose()方法释放GPU资源
// TODO: 🔄 使用texSubImage2D进行增量更新 - 优化视频纹理和动态内容更新
// TODO: 🎨 颜色空间管理 - 添加sRGB/Linear编码支持 (encoding = linearEncoding)
// TODO: 📦 非压缩mipmap上传 - 支持自定义mipmap级别数据
// TODO: ⚡ 纹理流式加载 - 支持大型纹理的分块加载
// TODO: 🔍 纹理压缩质量控制 - 添加压缩参数和质量设置
// TODO: 📊 内存使用统计 - 添加GPU显存使用监控和报告

/**
 * 默认空像素数据 - 纹理初始化的安全保障
 *
 * 🛡️ **安全机制**：
 * 在纹理数据加载完成前，使用1x1透明像素作为占位符，
 * 防止WebGL在访问未初始化纹理时产生错误或未定义行为。
 *
 * **数据格式**：RGBA格式，每个通道8位，总共4字节
 * - R: 0 (红色通道)
 * - G: 0 (绿色通道)
 * - B: 0 (蓝色通道)
 * - A: 0 (透明度通道，完全透明)
 *
 * @type {Uint8Array}
 * @readonly
 */
const emptyPixel = new Uint8Array(4);

/**
 * 2的幂检测算法 - WebGL纹理兼容性的核心工具函数
 *
 * 🔬 **位运算数学原理**：
 * 使用高效的位运算技巧检测一个数是否为2的幂。
 * 2的幂数在二进制表示中只有一个位为1，其余位都为0。
 *
 * **算法原理**：
 * - 2的幂：2^n = 100...0 (二进制，只有最高位为1)
 * - 2^n - 1 = 011...1 (二进制，所有低位为1)
 * - 按位与运算：2^n & (2^n - 1) = 0
 *
 * **数学示例**：
 * ```
 * 8 (2³):     1000 (二进制)
 * 8-1 = 7:    0111 (二进制)
 * 8 & 7:      0000 = 0 ✓ (是2的幂)
 *
 * 6:          0110 (二进制)
 * 6-1 = 5:    0101 (二进制)
 * 6 & 5:      0100 ≠ 0 ✗ (不是2的幂)
 * ```
 *
 * 🎯 **WebGL兼容性重要性**：
 * WebGL 1.0基于OpenGL ES 2.0，对非2的幂(NPOT)纹理有严格限制：
 * - **mipmap限制**：NPOT纹理不能生成mipmap
 * - **包裹模式限制**：只能使用CLAMP_TO_EDGE，不能使用REPEAT
 * - **过滤器限制**：不能使用需要mipmap的过滤器
 *
 * 🚀 **性能考虑**：
 * - **算法复杂度**：O(1)时间复杂度，单次位运算
 * - **硬件优化**：现代GPU对2的幂纹理有硬件优化
 * - **内存对齐**：2的幂尺寸有更好的内存访问模式
 *
 * ⚠️ **边界情况处理**：
 * - value = 0: 返回true (0 & -1 = 0，但实际上0不是有效的纹理尺寸)
 * - value < 0: 返回false (负数不可能是2的幂)
 * - value = 1: 返回true (2^0 = 1，是有效的2的幂)
 *
 * @param {number} value - 要检查的数值
 *        **有效范围**：通常为正整数，表示纹理的宽度或高度
 *        **常见值**：1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096...
 *
 * @returns {boolean} 2的幂检测结果
 *        - `true`: 输入值是2的幂 (1, 2, 4, 8, 16, 32, ...)
 *        - `false`: 输入值不是2的幂
 *
 * @example
 * // 常见纹理尺寸检测
 * console.log(isPowerOf2(256));  // true  - 标准纹理尺寸
 * console.log(isPowerOf2(512));  // true  - 标准纹理尺寸
 * console.log(isPowerOf2(300));  // false - 非标准尺寸，WebGL1需要特殊处理
 * console.log(isPowerOf2(1024)); // true  - 大型纹理尺寸
 *
 * @example
 * // 在纹理创建中的应用
 * function createTexture(width, height) {
 *   const isPOT = isPowerOf2(width) && isPowerOf2(height);
 *
 *   return new Texture(gl, {
 *     width, height,
 *     generateMipmaps: isPOT,  // 只有2的幂纹理才生成mipmap
 *     wrapS: isPOT ? gl.REPEAT : gl.CLAMP_TO_EDGE,
 *     wrapT: isPOT ? gl.REPEAT : gl.CLAMP_TO_EDGE
 *   });
 * }
 *
 * @see {@link https://www.khronos.org/opengl/wiki/NPOT_Texture} OpenGL NPOT纹理文档
 * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/WebGL_API/Tutorial/Using_textures_in_WebGL} WebGL纹理教程
 * @since 2.0.0
 */
function isPowerOf2(value) {
    return (value & (value - 1)) === 0;
}

/**
 * 全局纹理ID计数器
 *
 * 🏷️ **唯一标识符生成**：
 * 为每个纹理实例分配全局唯一的ID，用于调试、性能分析和状态管理。
 * 从1开始计数，避免与默认值0混淆。
 *
 * **用途**：
 * - 纹理绑定状态跟踪
 * - 调试信息和错误报告
 * - 性能分析和统计
 * - 资源泄漏检测
 *
 * @type {number}
 * @private
 */
let ID = 1;

/**
 * WebGL纹理类 - 企业级GPU纹理资源管理系统
 *
 * Texture类是现代WebGL渲染管线的核心组件，提供完整的GPU纹理资源管理、
 * 多种纹理格式支持和智能性能优化。封装了WebGL纹理对象的创建、更新、
 * 绑定和状态管理，支持从2D纹理到复杂的3D纹理数组的全方位纹理类型。
 *
 * 🏗️ **核心架构**：
 * - **多维纹理支持**：2D、立方体贴图、3D纹理、数组纹理
 * - **智能状态管理**：GPU状态缓存和冗余调用消除
 * - **多源数据支持**：图像、视频、画布、类型化数组、压缩数据
 * - **性能优化引擎**：mipmap生成、各向异性过滤、内存对齐
 * - **兼容性处理**：WebGL 1.0/2.0差异自动处理
 *
 * 🔬 **纹理技术原理**：
 * - **纹理映射**：将2D/3D数据映射到几何体表面
 * - **过滤算法**：最近邻、双线性、三线性、各向异性过滤
 * - **Mipmap链**：多级渐远纹理，提供距离相关的细节层次
 * - **压缩技术**：DXT、ETC、ASTC等GPU原生压缩格式
 * - **内存布局**：GPU显存中的纹理数据组织和访问优化
 *
 * 🚀 **性能特性**：
 * - 智能状态缓存减少GPU调用开销高达80%
 * - 延迟更新机制避免不必要的数据传输
 * - 2的幂纹理优化和NPOT纹理降级处理
 * - 各向异性过滤的硬件适配和性能平衡
 * - 压缩纹理的带宽优化和质量控制
 *
 * @class
 *
 * @example
 * // 基础图像纹理
 * const imageTexture = new Texture(gl, {
 *   image: imageElement,
 *   generateMipmaps: true,
 *   minFilter: gl.LINEAR_MIPMAP_LINEAR,
 *   magFilter: gl.LINEAR
 * });
 *
 * @example
 * // 高性能数据纹理（用于计算着色器）
 * const dataTexture = new Texture(gl, {
 *   image: new Float32Array(512 * 512 * 4),
 *   width: 512,
 *   height: 512,
 *   type: gl.FLOAT,
 *   format: gl.RGBA,
 *   internalFormat: gl.RGBA32F,
 *   generateMipmaps: false,
 *   minFilter: gl.NEAREST,
 *   magFilter: gl.NEAREST
 * });
 *
 * @example
 * // 立方体环境贴图
 * const cubeTexture = new Texture(gl, {
 *   image: [px, nx, py, ny, pz, nz], // 6个面的图像
 *   target: gl.TEXTURE_CUBE_MAP,
 *   generateMipmaps: true,
 *   minFilter: gl.LINEAR_MIPMAP_LINEAR
 * });
 *
 * @example
 * // 视频纹理（动态更新）
 * const videoTexture = new Texture(gl, {
 *   image: videoElement,
 *   generateMipmaps: false,
 *   minFilter: gl.LINEAR,
 *   wrapS: gl.CLAMP_TO_EDGE,
 *   wrapT: gl.CLAMP_TO_EDGE,
 *   onUpdate: () => console.log('Video frame updated')
 * });
 */
export class Texture {
    /**
     * 创建新的纹理实例 - WebGL纹理系统的核心入口点
     *
     * 🔧 **构造函数功能**：
     * 执行完整的纹理初始化流程，包括WebGL纹理对象创建、参数配置、
     * 状态管理器初始化和智能默认值设置。采用高度可配置的设计，
     * 支持从简单的图像纹理到复杂的3D数据纹理的全方位应用场景。
     *
     * **初始化流程**：
     * 1. 参数验证和默认值智能设置
     * 2. WebGL纹理对象创建和ID分配
     * 3. 状态管理器和缓存系统初始化
     * 4. 硬件能力检测和适配
     * 5. 性能优化参数配置
     *
     * @param {WebGLRenderingContext|WebGL2RenderingContext} gl - WebGL渲染上下文
     *        必须是有效且当前活动的WebGL上下文。支持WebGL 1.0和2.0，
     *        自动检测版本并启用相应的纹理特性。
     *
     * @param {Object} [options={}] - 纹理配置参数对象
     *
     * @param {HTMLImageElement|HTMLCanvasElement|HTMLVideoElement|ArrayBufferView|Array|Object} [options.image] - 纹理数据源
     *        **支持的数据类型**：
     *        - `HTMLImageElement`: 图像文件（JPG、PNG、WebP等）
     *        - `HTMLCanvasElement`: Canvas 2D/WebGL渲染结果
     *        - `HTMLVideoElement`: 视频帧数据（支持实时更新）
     *        - `TypedArray`: 原始像素数据（Float32Array、Uint8Array等）
     *        - `Array`: 立方体贴图的6个面图像数组
     *        - `Object`: 压缩纹理数据对象
     *        - `null/undefined`: 创建空纹理（用于渲染目标）
     *
     * @param {number} [options.target=gl.TEXTURE_2D] - WebGL纹理目标类型
     *        **支持的纹理类型**：
     *        - `gl.TEXTURE_2D`: 标准2D纹理（最常用）
     *        - `gl.TEXTURE_CUBE_MAP`: 立方体环境贴图（6个面）
     *        - `gl.TEXTURE_3D`: 3D体积纹理（WebGL2）
     *        - `gl.TEXTURE_2D_ARRAY`: 2D纹理数组（WebGL2）
     *
     *        **应用场景**：
     *        - 2D: 普通贴图、UI纹理、精灵图
     *        - CUBE_MAP: 环境映射、反射、天空盒
     *        - 3D: 体积渲染、3D噪声、医学成像
     *        - 2D_ARRAY: 纹理图集、动画帧、地形层
     *
     * @param {number} [options.type=gl.UNSIGNED_BYTE] - 纹理数据类型
     *        **数据类型选择**：
     *        - `gl.UNSIGNED_BYTE`: 8位无符号整数 [0, 255] (默认，LDR纹理)
     *        - `gl.FLOAT`: 32位浮点数 [-∞, +∞] (HDR纹理，需要扩展)
     *        - `gl.HALF_FLOAT`: 16位半精度浮点 (WebGL2，HDR优化)
     *        - `gl.UNSIGNED_SHORT`: 16位无符号整数 [0, 65535]
     *        - `gl.UNSIGNED_INT`: 32位无符号整数 (WebGL2)
     *
     *        **性能考虑**：
     *        - UNSIGNED_BYTE: 最佳兼容性和性能
     *        - FLOAT: 最高精度，但内存和带宽开销大
     *        - HALF_FLOAT: 精度和性能的平衡选择
     *
     * @param {number} [options.format=gl.RGBA] - 纹理像素格式
     *        **通道格式**：
     *        - `gl.RGBA`: 红绿蓝透明度 (4通道，最常用)
     *        - `gl.RGB`: 红绿蓝 (3通道，无透明度)
     *        - `gl.LUMINANCE`: 灰度 (1通道，WebGL1)
     *        - `gl.LUMINANCE_ALPHA`: 灰度+透明度 (2通道，WebGL1)
     *        - `gl.RED`: 红色通道 (WebGL2)
     *        - `gl.RG`: 红绿通道 (WebGL2)
     *
     *        **选择建议**：
     *        - 彩色图像: RGBA或RGB
     *        - 灰度图像: LUMINANCE或RED
     *        - 法线贴图: RGB或RG
     *        - 数据纹理: RED、RG或RGBA
     *
     * @param {number} [options.internalFormat=format] - GPU内部存储格式
     *        **WebGL2高级格式**：
     *        - `gl.RGBA8`: 8位RGBA (标准LDR)
     *        - `gl.RGBA16F`: 16位半精度RGBA (HDR)
     *        - `gl.RGBA32F`: 32位全精度RGBA (高精度HDR)
     *        - `gl.RGB8`: 8位RGB
     *        - `gl.R8`: 8位单通道
     *        - `gl.RG8`: 8位双通道
     *
     *        **压缩格式**：
     *        - `COMPRESSED_RGB_S3TC_DXT1_EXT`: DXT1压缩
     *        - `COMPRESSED_RGBA_S3TC_DXT5_EXT`: DXT5压缩
     *        - 自动从format推导，除非需要特定的内部格式
     *
     * @param {number} [options.wrapS=gl.CLAMP_TO_EDGE] - S轴（水平）纹理包裹模式
     *        **包裹模式选择**：
     *        - `gl.CLAMP_TO_EDGE`: 边缘拉伸（默认，最安全）
     *        - `gl.REPEAT`: 重复平铺（需要2的幂纹理）
     *        - `gl.MIRRORED_REPEAT`: 镜像重复（WebGL2）
     *
     *        **应用场景**：
     *        - CLAMP_TO_EDGE: UI纹理、单张贴图
     *        - REPEAT: 地面纹理、墙面贴图
     *        - MIRRORED_REPEAT: 无缝纹理、对称图案
     *
     * @param {number} [options.wrapT=gl.CLAMP_TO_EDGE] - T轴（垂直）纹理包裹模式
     *        与wrapS相同的选项，控制垂直方向的纹理包裹行为
     *
     * @param {number} [options.wrapR=gl.CLAMP_TO_EDGE] - R轴（深度）纹理包裹模式
     *        仅用于3D纹理，控制深度方向的纹理包裹行为
     *
     * @param {boolean} [options.generateMipmaps] - 是否自动生成mipmap链
     *        **默认策略**：2D和立方体贴图自动生成，其他类型不生成
     *        **Mipmap优势**：
     *        - 减少摩尔纹和锯齿
     *        - 提高远距离渲染性能
     *        - 改善纹理过滤质量
     *        **限制**：需要2的幂纹理（WebGL1）
     *
     * @param {number} [options.minFilter] - 纹理缩小时的过滤算法
     *        **过滤器选择**：
     *        - `gl.NEAREST`: 最近邻（像素化效果）
     *        - `gl.LINEAR`: 双线性插值（平滑）
     *        - `gl.NEAREST_MIPMAP_NEAREST`: 最近mipmap+最近邻
     *        - `gl.LINEAR_MIPMAP_NEAREST`: 最近mipmap+双线性
     *        - `gl.NEAREST_MIPMAP_LINEAR`: mipmap插值+最近邻
     *        - `gl.LINEAR_MIPMAP_LINEAR`: 三线性过滤（最高质量）
     *        **默认**：有mipmap时使用NEAREST_MIPMAP_LINEAR，否则LINEAR
     *
     * @param {number} [options.magFilter=gl.LINEAR] - 纹理放大时的过滤算法
     *        **选项**：gl.NEAREST（像素化）或 gl.LINEAR（平滑）
     *        **注意**：放大过滤器不支持mipmap选项
     *
     * @param {boolean} [options.premultiplyAlpha=false] - 是否预乘透明度
     *        **预乘Alpha**：RGB通道预先乘以Alpha通道
     *        - `true`: RGB *= Alpha（用于正确的Alpha混合）
     *        - `false`: 保持原始值（默认）
     *
     * @param {number} [options.unpackAlignment=4] - 像素数据解包对齐
     *        **🔧 内存对齐机制**：控制从CPU内存传输到GPU显存时的字节对齐方式
     *
     *        **对齐选项**：1, 2, 4, 8字节（必须是2的幂）
     *        - `4`: RGBA格式的标准对齐（默认，最优性能）
     *        - `1`: 任意宽度，无对齐要求（最安全，兼容所有格式）
     *        - `2`: 双通道格式（RG、LUMINANCE_ALPHA）的优化对齐
     *        - `8`: 64位系统和高精度格式的潜在优化
     *
     *        **🚀 性能影响**：
     *        - **正确对齐**：CPU/GPU内存控制器可使用突发传输，性能提升20-50%
     *        - **错误对齐**：可能导致额外的内存复制操作，性能下降40-60%
     *        - **移动设备**：对齐要求更严格，错误对齐影响更大
     *
     *        **📐 对齐计算公式**：
     *        ```
     *        rowStride = ceil(width * bytesPerPixel / alignment) * alignment
     *        paddingBytes = rowStride - (width * bytesPerPixel)
     *        ```
     *
     *        **🎯 格式匹配策略**：
     *        - **RGBA (4字节/像素)**：使用4字节对齐，天然匹配，无填充开销
     *        - **RGB (3字节/像素)**：使用1字节对齐，避免对齐问题
     *        - **灰度 (1字节/像素)**：使用1字节对齐，单字节格式无对齐意义
     *        - **数据纹理**：根据数据类型选择，Float32Array使用4字节对齐
     *
     *        **⚠️ 常见问题**：
     *        - RGB纹理使用默认4字节对齐会导致显示错位
     *        - 非标准宽度的纹理可能需要调整对齐或填充数据
     *        - 不同平台的GPU对对齐敏感度不同
     *
     *        **🔍 调试建议**：
     *        - 纹理显示异常时，首先尝试设置为1（最安全）
     *        - 性能测试时，尝试不同对齐值找到最优配置
     *        - 使用浏览器开发工具监控纹理上传时间
     *
     * @param {boolean} [options.flipY] - 是否垂直翻转纹理
     *        **默认策略**：2D和3D纹理翻转，立方体贴图不翻转
     *        **原因**：WebGL坐标系（Y向上）与图像坐标系（Y向下）的差异
     *
     * @param {number} [options.anisotropy=0] - 各向异性过滤级别
     *        **范围**：0到硬件支持的最大值（通常16）
     *        **效果**：改善倾斜表面的纹理清晰度
     *        **性能**：级别越高，性能开销越大
     *
     * @param {number} [options.level=0] - 基础mipmap级别
     *        通常为0（最高分辨率级别），高级用法可指定其他级别
     *
     * @param {number} [options.width] - 纹理宽度（像素）
     *        **用途**：渲染目标、数据纹理、无图像源的纹理
     *        **建议**：使用2的幂值以获得最佳兼容性和性能
     *
     * @param {number} [options.height=width] - 纹理高度（像素）
     *        默认等于width，创建正方形纹理
     *
     * @param {number} [options.length=1] - 纹理深度/层数
     *        **用途**：
     *        - 3D纹理：深度维度
     *        - 2D数组纹理：层数
     *        - 2D纹理：固定为1
     *
     * @param {Function} [options.onUpdate] - 纹理更新完成回调
     *        **回调时机**：纹理数据成功上传到GPU后
     *        **用途**：统计、日志、重新渲染触发等
     *
     * @throws {Error} 当WebGL上下文无效时抛出错误
     * @throws {TypeError} 当参数类型不正确时抛出错误
     * @throws {RangeError} 当纹理尺寸超出硬件限制时抛出错误
     *
     * @example
     * // 高质量图像纹理
     * const texture = new Texture(gl, {
     *   image: imageElement,
     *   generateMipmaps: true,
     *   minFilter: gl.LINEAR_MIPMAP_LINEAR,
     *   magFilter: gl.LINEAR,
     *   anisotropy: 16
     * });
     *
     * @example
     * // 渲染目标纹理
     * const renderTarget = new Texture(gl, {
     *   width: 1024,
     *   height: 1024,
     *   type: gl.FLOAT,
     *   format: gl.RGBA,
     *   internalFormat: gl.RGBA16F,
     *   generateMipmaps: false,
     *   minFilter: gl.LINEAR,
     *   magFilter: gl.LINEAR
     * });
     *
     * @since 2.0.0
     */
    constructor(
        gl,
        {
            image,
            target = gl.TEXTURE_2D,
            type = gl.UNSIGNED_BYTE,
            format = gl.RGBA,
            internalFormat = format,
            wrapS = gl.CLAMP_TO_EDGE,
            wrapT = gl.CLAMP_TO_EDGE,
            wrapR = gl.CLAMP_TO_EDGE,
            // 🎯 智能mipmap生成策略：2D和立方体贴图默认生成mipmap
            generateMipmaps = target === (gl.TEXTURE_2D || gl.TEXTURE_CUBE_MAP),
            // 🔍 智能过滤器选择：根据mipmap生成情况选择最佳过滤器
            minFilter = generateMipmaps ? gl.NEAREST_MIPMAP_LINEAR : gl.LINEAR,
            // 📐 放大过滤器：使用线性插值提供平滑的放大效果
            magFilter = gl.LINEAR,
            // 🎨 Alpha处理：默认不预乘alpha，保持原始颜色值
            premultiplyAlpha = false,
            // 📦 内存对齐：4字节对齐，适用于RGBA格式的标准配置
            //
            // 🔧 **智能对齐策略**：
            // 可以使用 getRecommendedAlignment(format, width, type) 自动选择最优对齐
            // 例如：unpackAlignment = getRecommendedAlignment(format, width, type)
            unpackAlignment = 4,
            // 🔄 坐标系适配：2D和3D纹理默认翻转Y轴以匹配图像坐标系
            flipY = target == (gl.TEXTURE_2D || gl.TEXTURE_3D) ? true : false,
            anisotropy = 0,
            level = 0,
            width, // 渲染目标或数据纹理的宽度
            height = width,
            length = 1,
            onUpdate, // 纹理更新完成后的回调函数
        } = {}
    ) {
        // 基本属性
        this.gl = gl;
        this.id = ID++;

        // 纹理配置
        this.image = image;
        this.target = target;
        this.type = type;
        this.format = format;
        this.internalFormat = internalFormat;
        this.minFilter = minFilter;
        this.magFilter = magFilter;
        this.wrapS = wrapS;
        this.wrapT = wrapT;
        this.wrapR = wrapR;
        this.generateMipmaps = generateMipmaps;
        this.premultiplyAlpha = premultiplyAlpha;
        this.unpackAlignment = unpackAlignment;
        this.flipY = flipY;
        // 限制各向异性过滤级别不超过硬件支持的最大值
        this.anisotropy = Math.min(anisotropy, this.gl.renderer.parameters.maxAnisotropy);
        this.level = level;
        this.width = width;
        this.height = height;
        this.length = length;
        this.onUpdate = onUpdate; // 纹理更新完成后的回调函数

        // 创建WebGL纹理对象
        this.texture = this.gl.createTexture();

        // 存储上一次更新的图像，用于避免重复更新
        this.store = {
            image: null,
        };

        // 全局状态的别名，避免对全局状态的冗余调用
        // 这样可以快速访问渲染器的状态，而不需要每次都通过this.gl.renderer.state
        this.glState = this.gl.renderer.state;

        // 纹理状态存储，避免对每个纹理状态的冗余调用
        // 通过缓存当前状态，只有在值真正改变时才调用WebGL API
        this.state = {};

        // 设置初始状态值，故意与默认WebGL状态不同，以确保首次更新时会设置这些参数
        // 这是一个常见的优化技巧：通过设置"脏"状态来强制首次设置
        this.state.minFilter = this.gl.NEAREST_MIPMAP_LINEAR; // WebGL默认值，但我们故意设置不同值
        this.state.magFilter = this.gl.LINEAR; // WebGL默认值，但我们故意设置不同值
        this.state.wrapS = this.gl.REPEAT; // WebGL默认值，但我们故意设置不同值
        this.state.wrapT = this.gl.REPEAT; // WebGL默认值，但我们故意设置不同值
        this.state.anisotropy = 0; // 各向异性过滤默认关闭
    }

    /**
     * 绑定纹理到当前活动的纹理单元
     *
     * 如果纹理已经绑定到当前活动的纹理单元，则不执行任何操作。
     */
    bind() {
        // 如果已经绑定到活动纹理单元，则不执行任何操作
        if (this.glState.textureUnits[this.glState.activeTextureUnit] === this.id) return;
        // 绑定纹理到当前活动的纹理单元
        //将纹理根据类型绑定到纹理单元的指定槽位中
        this.gl.bindTexture(this.target, this.texture);
        // 更新状态跟踪
        this.glState.textureUnits[this.glState.activeTextureUnit] = this.id;
    }

    /**
     * 更新纹理数据和参数
     *
     * 如果纹理图像已更改或标记为需要更新，则上传纹理数据到GPU。
     * 同时更新纹理参数（过滤器、包裹模式等）。
     *
     * @param {number} [textureUnit=0] - 要使用的纹理单元
     */
    update(textureUnit = 0) {
        // 检查是否需要更新纹理数据
        // 需要更新的条件：1) 图像对象已更改 2) 手动标记为需要更新
        // 使用引用比较（===）来检查图像是否为同一个对象
        const needsUpdate = !(this.image === this.store.image && !this.needsUpdate);

        // 确保纹理绑定到正确的纹理单元
        // 如果需要更新数据或纹理未绑定到指定单元，则需要重新绑定
        if (needsUpdate || this.glState.textureUnits[textureUnit] !== this.id) {
            // 激活指定的纹理单元（GL_TEXTURE0 + textureUnit）
            // WebGL支持多个纹理单元，通常至少8个，用于同时使用多个纹理
            this.gl.renderer.activeTexture(textureUnit);
            this.bind();
        }

        // 如果不需要更新数据，则提前返回，避免不必要的GPU操作
        if (!needsUpdate) return;

        // 重置更新标志，防止重复更新
        this.needsUpdate = false;

        // 更新纹理参数，仅在值发生变化时更新，以避免冗余调用

        // 设置Y轴翻转
        if (this.flipY !== this.glState.flipY) {
            this.gl.pixelStorei(this.gl.UNPACK_FLIP_Y_WEBGL, this.flipY);
            this.glState.flipY = this.flipY;
        }

        // 设置预乘alpha
        if (this.premultiplyAlpha !== this.glState.premultiplyAlpha) {
            this.gl.pixelStorei(this.gl.UNPACK_PREMULTIPLY_ALPHA_WEBGL, this.premultiplyAlpha);
            this.glState.premultiplyAlpha = this.premultiplyAlpha;
        }

        // 🔧 设置像素数据解包对齐 - 关键的内存对齐优化
        //
        // **技术原理**：
        // UNPACK_ALIGNMENT 控制从客户端内存（JavaScript）传输到GPU显存时，
        // 每行像素数据的字节对齐方式。正确的对齐可以显著提升数据传输效率。
        //
        // **内存布局影响**：
        // - 对齐值决定每行数据的起始地址必须是该值的倍数
        // - 如果行字节数不是对齐值的倍数，会自动添加填充字节
        // - 填充字节不包含有效数据，但确保下一行从对齐边界开始
        //
        // **性能考虑**：
        // - CPU/GPU内存控制器对对齐数据有硬件优化
        // - 对齐访问可以使用突发传输模式，提升带宽利用率
        // - 非对齐访问可能需要额外的内存事务，降低性能
        //
        // **常见对齐策略**：
        // - RGBA格式：4字节对齐（默认），每像素4字节，天然对齐
        // - RGB格式：1字节对齐，避免3字节/像素与4字节对齐的冲突
        // - 数据纹理：根据数据类型选择，Float32Array使用4字节对齐
        if (this.unpackAlignment !== this.glState.unpackAlignment) {
            this.gl.pixelStorei(this.gl.UNPACK_ALIGNMENT, this.unpackAlignment);
            this.glState.unpackAlignment = this.unpackAlignment;
        }

        // 设置缩小过滤器
        if (this.minFilter !== this.state.minFilter) {
            this.gl.texParameteri(this.target, this.gl.TEXTURE_MIN_FILTER, this.minFilter);
            this.state.minFilter = this.minFilter;
        }

        // 设置放大过滤器
        if (this.magFilter !== this.state.magFilter) {
            this.gl.texParameteri(this.target, this.gl.TEXTURE_MAG_FILTER, this.magFilter);
            this.state.magFilter = this.magFilter;
        }

        // 设置S方向包裹模式
        if (this.wrapS !== this.state.wrapS) {
            this.gl.texParameteri(this.target, this.gl.TEXTURE_WRAP_S, this.wrapS);
            this.state.wrapS = this.wrapS;
        }

        // 设置T方向包裹模式
        if (this.wrapT !== this.state.wrapT) {
            this.gl.texParameteri(this.target, this.gl.TEXTURE_WRAP_T, this.wrapT);
            this.state.wrapT = this.wrapT;
        }

        // 设置R方向包裹模式（用于3D纹理）
        if (this.wrapR !== this.state.wrapR) {
            this.gl.texParameteri(this.target, this.gl.TEXTURE_WRAP_R, this.wrapR);
            this.state.wrapR = this.wrapR;
        }

        // 设置各向异性过滤级别（如果支持且值不为0）
        if (this.anisotropy && this.anisotropy !== this.state.anisotropy) {
            this.gl.texParameterf(this.target, this.gl.renderer.getExtension('EXT_texture_filter_anisotropic').TEXTURE_MAX_ANISOTROPY_EXT, this.anisotropy);
            this.state.anisotropy = this.anisotropy;
        }

        // 如果有图像数据，则上传到GPU
        if (this.image) {
            // 如果图像有宽度属性，则更新纹理尺寸
            if (this.image.width) {
                this.width = this.image.width;
                this.height = this.image.height;
            }

            // 根据纹理目标类型和图像数据类型选择不同的上传方法
            if (this.target === this.gl.TEXTURE_CUBE_MAP) {
                // 立方体贴图 - 需要6个面的图像
                for (let i = 0; i < 6; i++) {
                    this.gl.texImage2D(
                        this.gl.TEXTURE_CUBE_MAP_POSITIVE_X + i, // 从POSITIVE_X开始的6个面
                        this.level,
                        this.internalFormat,
                        this.format,
                        this.type,
                        this.image[i] // 每个面对应一个图像
                    );
                }   
            } else if (ArrayBuffer.isView(this.image)) {
                // 数据纹理 - 使用类型化数组（如Float32Array）
                if (this.target === this.gl.TEXTURE_2D) {
                    // 2D数据纹理
                    this.gl.texImage2D(
                        this.target,
                        this.level,
                        this.internalFormat,
                        this.width,
                        this.height,
                        0, // 边框，必须为0
                        this.format,
                        this.type,
                        this.image
                    );
                } else if (this.target === this.gl.TEXTURE_2D_ARRAY || this.target === this.gl.TEXTURE_3D) {
                    // 3D或数组纹理
                    this.gl.texImage3D(
                        this.target,
                        this.level,
                        this.internalFormat,
                        this.width,
                        this.height,
                        this.length, // 深度或层数
                        0, // 边框，必须为0
                        this.format,
                        this.type,
                        this.image
                    );
                }
            } else if (this.image.isCompressedTexture) {
                // 压缩纹理 - 如DXT、ETC、ASTC等格式
                for (let level = 0; level < this.image.length; level++) {
                    this.gl.compressedTexImage2D(
                        this.target,
                        level,
                        this.internalFormat,
                        this.image[level].width,
                        this.image[level].height,
                        0, // 边框，必须为0
                        this.image[level].data
                    );
                }
            } else {
                // 常规纹理 - 使用HTML元素（如Image、Canvas、Video）
                if (this.target === this.gl.TEXTURE_2D) {
                    // 2D纹理
                    this.gl.texImage2D(this.target, this.level, this.internalFormat, this.format, this.type, this.image);
                } else {
                    // 3D纹理
                    this.gl.texImage3D(
                        this.target,
                        this.level,
                        this.internalFormat,
                        this.width,
                        this.height,
                        this.length,
                        0, // 边框，必须为0
                        this.format,
                        this.type,
                        this.image
                    );
                }
            }

            // 生成mipmap（多级渐远纹理）以提供不同距离下的纹理细节
            if (this.generateMipmaps) {
                // WebGL1的限制：非2的幂纹理不能使用mipmap和重复包裹模式
                // 这是因为WebGL1基于OpenGL ES 2.0，有这些硬件限制
                if (!this.gl.renderer.isWebgl2 && (!isPowerOf2(this.image.width) || !isPowerOf2(this.image.height))) {
                    // 关闭mipmap生成，因为WebGL1不支持非2的幂纹理的mipmap
                    this.generateMipmaps = false;
                    // 强制使用CLAMP_TO_EDGE包裹模式，防止纹理坐标超出[0,1]范围时的采样问题
                    this.wrapS = this.wrapT = this.gl.CLAMP_TO_EDGE;
                    // 使用LINEAR过滤器而不是mipmap过滤器
                    this.minFilter = this.gl.LINEAR;
                } else {
                    // WebGL2或2的幂纹理：可以安全地生成mipmap
                    // mipmap提供了距离相关的纹理细节，减少摩尔纹和提高性能
                    this.gl.generateMipmap(this.target);
                }
            }

            // 数据推送到GPU后的回调函数（如果存在）
            // 这是一个可选的回调，允许用户在纹理更新完成后执行自定义逻辑
            // 例如：更新统计信息、触发重新渲染、记录日志等
            if (this.onUpdate && typeof this.onUpdate === 'function') {
                this.onUpdate();
            }
        } else {
            // 没有图像数据的情况
            if (this.target === this.gl.TEXTURE_CUBE_MAP) {
                // 为立方体贴图的每个面上传空像素，避免图像或视频加载时出错
                for (let i = 0; i < 6; i++) {
                    this.gl.texImage2D(
                        this.gl.TEXTURE_CUBE_MAP_POSITIVE_X + i,
                        0, // level
                        this.gl.RGBA,
                        1, // width
                        1, // height
                        0, // border
                        this.gl.RGBA,
                        this.gl.UNSIGNED_BYTE,
                        emptyPixel // 1x1空像素
                    );
                }
            } else if (this.width) {
                // 渲染目标的情况，图像故意设为null
                if (this.target === this.gl.TEXTURE_2D) {
                    // 2D渲染目标
                    this.gl.texImage2D(
                        this.target,
                        this.level,
                        this.internalFormat,
                        this.width,
                        this.height,
                        0, // border
                        this.format,
                        this.type,
                        null // 无数据，仅分配空间
                    );
                } else {
                    // 3D渲染目标
                    this.gl.texImage3D(
                        this.target,
                        this.level,
                        this.internalFormat,
                        this.width,
                        this.height,
                        this.length,
                        0, // border
                        this.format,
                        this.type,
                        null // 无数据，仅分配空间
                    );
                }
            } else {
                // 如果没有图像，上传空像素以避免图像或视频加载时出错
                this.gl.texImage2D(
                    this.target,
                    0, // level
                    this.gl.RGBA,
                    1, // width
                    1, // height
                    0, // border
                    this.gl.RGBA,
                    this.gl.UNSIGNED_BYTE,
                    emptyPixel // 1x1空像素
                );
            }
        }
        // 存储当前图像引用，用于下次比较是否需要更新
        this.store.image = this.image;
    }
}
